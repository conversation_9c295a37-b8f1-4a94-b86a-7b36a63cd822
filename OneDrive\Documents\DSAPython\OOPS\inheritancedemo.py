class Student:
    def __init__(self,args):
        self.name=args[0]
        self.age=args[1]
        self.rollno=args[2]
    def display(self):
        print(f"name:{self.name} age:{self.age} rollno:{self.rollno}")
class Marks(Student):
    def __init__(self,args,marks):
        super().__init__(args)
        self.marks=marks
        print("child class")
    def displaymarks(self):
        super().display()
        print(f"marks:{self.marks}")

m=Marks("raju 21 1002".split(),[90,45,60,70,80])
m.display()
m.displaymarks()